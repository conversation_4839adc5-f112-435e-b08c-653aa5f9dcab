# Parseable Server (C) 2022 - 2024 Parseable, Inc.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as
# published by the Free Software Foundation, either version 3 of the
# License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

# build stage
FROM  rust:1.84.0-bookworm AS builder

LABEL org.opencontainers.image.title="Parseable"
LABEL maintainer="Parseable Team <<EMAIL>>"
LABEL org.opencontainers.image.vendor="Parseable Inc"
LABEL org.opencontainers.image.licenses="AGPL-3.0"

RUN apt-get update && \
    apt-get install --no-install-recommends -y \
    cmake \
    clang \
    librdkafka-dev \
    ca-certificates \
    build-essential \
    libsasl2-dev \
    libssl-dev && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /parseable
COPY Cargo.toml Cargo.lock build.rs ./

# Fix librdkafka CMakeLists.txt before building
RUN mkdir -p src && echo "fn main() {}" > src/main.rs && \
    # Download the package so it's in the cargo registry
    cargo fetch && \
    # Find rdkafka-sys directory
    RDKAFKA_SYS_DIR=$(find /usr/local/cargo/registry/src -name "rdkafka-sys-*" -type d | head -n 1) && \
    echo "Found rdkafka-sys at: $RDKAFKA_SYS_DIR" && \
    # Find the CMakeLists.txt file
    CMAKE_FILE="$RDKAFKA_SYS_DIR/librdkafka/CMakeLists.txt" && \
    if [ -f "$CMAKE_FILE" ]; then \
    echo "Found CMakeLists.txt at: $CMAKE_FILE" && \
    # Replace the minimum required version
    sed -i 's/cmake_minimum_required(VERSION 3.2)/cmake_minimum_required(VERSION 3.5)/' "$CMAKE_FILE" && \
    echo "Modified CMakeLists.txt to use CMake 3.5 minimum version"; \
    else \
    echo "Could not find librdkafka CMakeLists.txt file!" && \
    exit 1; \
    fi

# Now build dependencies with the fixed CMakeLists.txt
RUN cargo build --release --features kafka && \
    rm -rf src

# Copy the actual source code
COPY src ./src
COPY resources ./resources

# Build the actual binary with kafka feature
RUN cargo build --release --features kafka

# final stage
FROM gcr.io/distroless/cc-debian12:latest

# Copy only the libraries that binary needs since kafka is statically linked
ARG LIB_DIR
COPY --from=builder /usr/lib/${LIB_DIR}/libsasl2.so.2 /usr/lib/${LIB_DIR}/
COPY --from=builder /usr/lib/${LIB_DIR}/libssl.so.3   /usr/lib/${LIB_DIR}/
COPY --from=builder /usr/lib/${LIB_DIR}/libcrypto.so.3 /usr/lib/${LIB_DIR}/

WORKDIR /parseable

# Copy the Parseable binary from builder
COPY --from=builder /parseable/target/release/parseable /usr/bin/parseable

# Copy CA certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/


CMD ["/usr/bin/parseable"]
