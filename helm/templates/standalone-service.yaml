{{- if eq .Values.parseable.highAvailability.enabled false }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "parseable.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels: 
    {{- include "parseable.labelsSelector" . | nindent 4 }}
spec:
  type: {{ $.Values.parseable.service.type }}
  ports:
    - port: {{ $.Values.parseable.service.port }}
      targetPort: 8000
      protocol: TCP
  selector:
    {{- include "parseable.labelsSelector" . | nindent 4 }}
{{- end }}
