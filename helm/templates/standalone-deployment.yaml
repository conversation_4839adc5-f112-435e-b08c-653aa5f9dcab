{{- if eq .Values.parseable.highAvailability.enabled false }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "parseable.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "parseable.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "parseable.labelsSelector" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.parseable.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- .Values.parseable.podLabels | toYaml | nindent 8 }}
        {{- include "parseable.labelsSelector" . | nindent 8 }}
    spec:
      {{- with .Values.parseable.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "parseable.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.parseable.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.parseable.securityContext | nindent 12 }}
          image: "{{ .Values.parseable.image.repository }}:{{ .Values.parseable.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.parseable.image.pullPolicy }}
          # Uncomment to debug
          # command: [ "/bin/sh", "-c", "sleep 1000000" ]
          args: [ "/usr/bin/parseable", {{ if eq .Values.parseable.store "gcs-store" }}"s3-store"{{ else }}{{ .Values.parseable.store | quote }}{{ end }}]
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            {{- range $key, $value :=  .Values.parseable.env }}
            - name: {{ $key }}
              value: {{ tpl $value $ | quote }}
            {{- end }}
            {{- if and .Values.parseable.localModeSecret .Values.parseable.localModeSecret.enabled }}
            {{- range $secret := .Values.parseable.localModeSecret.secrets }}
            {{- range $key := $secret.keys }}
            {{- $envPrefix := $secret.prefix | default "" | upper }}
            {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
            - name: {{ $envPrefix }}{{ $envKey }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secret.name }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
            {{- end }}

            {{- if .Values.parseable.auditLogging.enabled }}
            - name: P_AUDIT_LOGGER
              value: {{ .Values.parseable.auditLogging.p_server | quote }}
            - name: P_AUDIT_USERNAME
              value: {{ .Values.parseable.auditLogging.p_username | quote }}
            - name: P_AUDIT_PASSWORD
              value: {{ .Values.parseable.auditLogging.p_password | quote }}
            {{- end }}

            {{- if .Values.parseable.kafkaConnector.enabled }}
            {{- range $key, $value := .Values.parseable.kafkaConnector.env }}
            - name: {{ $key }}
              value: {{ tpl $value $ | quote }}
            {{- end }}
            {{- end }}

            {{- if and .Values.parseable.s3ModeSecret .Values.parseable.s3ModeSecret.enabled }}
            {{- range $secret := .Values.parseable.s3ModeSecret.secrets }}
            {{- range $key := $secret.keys }}
            {{- $envPrefix := $secret.prefix | default "" | upper }}
            {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
            - name: {{ $envPrefix }}{{ $envKey }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secret.name }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
            {{- end }}

            {{- if and .Values.parseable.gcsModeSecret .Values.parseable.gcsModeSecret.enabled }}
            {{- range $secret := .Values.parseable.gcsModeSecret.secrets }}
            {{- range $key := $secret.keys }}
            {{- $envPrefix := $secret.prefix | default "" | upper }}
            {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
            - name: {{ $envPrefix }}{{ $envKey | replace "GCS" "S3"}}
              valueFrom:
                secretKeyRef:
                  name: {{ $secret.name }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
            {{- end }}

            {{- if and .Values.parseable.blobModeSecret .Values.parseable.blobModeSecret.enabled }}
            {{- range $secret := .Values.parseable.blobModeSecret.secrets }}
            {{- range $key := $secret.keys }}
            {{- $envPrefix := $secret.prefix | default "" | upper }}
            {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
            - name: {{ $envPrefix }}{{ $envKey }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secret.name }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
            {{- end }}


          ports:
            - containerPort: 8000
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{ toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.parseable.resources | nindent 12 }}
          volumeMounts:
          - mountPath: "/parseable/data"
            name: data-volume
          - mountPath: "/parseable/staging"
            name: stage-volume
      volumes:
      {{- if .Values.parseable.persistence.staging.enabled }}
      - name: stage-volume
        persistentVolumeClaim:
          claimName: {{ include "parseable.fullname" . }}-staging-pvc
      {{- else }}
      - name: stage-volume
        emptyDir: {}
      {{- end }}
      {{- if .Values.parseable.persistence.data.enabled }}
      - name: data-volume
        persistentVolumeClaim:
          claimName: {{ include "parseable.fullname" . }}-data-pvc
      {{- else }}
      - name: data-volume
        emptyDir: {}
      {{- end }}
      {{- with .Values.parseable.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.parseable.toleration }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}  # Closing for "if eq .Values.parseable.highAvailability.enabled false"
