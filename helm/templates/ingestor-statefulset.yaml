{{- if eq .Values.parseable.highAvailability.enabled true }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "parseable.fullname" . }}-ingestor-headless
  namespace: {{ .Release.Namespace }}
spec:
  ports:
    - port: 8000
      name: "parseable-port"
  clusterIP: None
  selector:
    {{- include "parseable.ingestorLabelsSelector" . | nindent 4 }}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "parseable.fullname" . }}-ingestor
  namespace: {{ .Release.Namespace }}
  labels:
    {{- with .Values.parseable.highAvailability.ingestor.extraLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "parseable.ingestorLabelsSelector" . | nindent 6 }}
  serviceName: {{ include "parseable.fullname" . }}-ingestor-headless
  replicas: {{ .Values.parseable.highAvailability.ingestor.count }}
  minReadySeconds: 2
  template:
    metadata:
      labels:
        {{- .Values.parseable.highAvailability.ingestor.labels | toYaml | nindent 8 }}
        {{- include "parseable.ingestorLabelsSelector" . | nindent 8 }}
    spec:
      {{- with .Values.parseable.highAvailability.ingestor.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      initContainers:
        - name: wait-for-query-service
          image: curlimages/curl:latest # Lightweight image with curl
          command:
            - /bin/sh
            - -c
            - |
              echo "Waiting for query service readiness at /api/v1/readiness..."
              for i in $(seq 1 60); do
                if curl -sf http://{{ include "parseable.fullname" . }}-querier-service.{{ .Release.Namespace }}/api/v1/readiness; then
                  echo "Query service is ready!"
                  exit 0
                fi
                echo "Query service is not ready yet. Retrying in 2 seconds..."
                sleep 2
              done
              echo "Query service did not become ready in time. Exiting."
              exit 1
      terminationGracePeriodSeconds: 10
      serviceAccountName: {{ include "parseable.serviceAccountName" . }}
      {{- with .Values.parseable.highAvailability.ingestor.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.parseable.highAvailability.ingestor.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        securityContext:
          {{- toYaml .Values.parseable.securityContext | nindent 10 }}
        image: {{ .Values.parseable.image.repository }}:{{ .Values.parseable.image.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.parseable.image.pullPolicy }}
        args:
          - /usr/bin/parseable
          - {{ if eq .Values.parseable.store "gcs-store" }}"s3-store"{{ else }}{{ .Values.parseable.store | quote }}{{ end }}
          - --ingestor-endpoint=$(HOSTNAME).{{ include "parseable.fullname" . }}-ingestor-headless.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.parseable.highAvailability.ingestor.port }}
        env:
          {{- range $key, $value :=  .Values.parseable.highAvailability.ingestor.env }}
          - name: {{ $key }}
            value: {{ tpl $value $ | quote }}
          - name: HOSTNAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          {{- end }}
          
          {{- if .Values.parseable.auditLogging.enabled }}
          - name: P_AUDIT_LOGGER
            value: {{ .Values.parseable.auditLogging.p_server | quote }}
          - name: P_AUDIT_USERNAME
            value: {{ .Values.parseable.auditLogging.p_username | quote }}
          - name: P_AUDIT_PASSWORD
            value: {{ .Values.parseable.auditLogging.p_password | quote }}
          {{- end }}

          {{- if and .Values.parseable.s3ModeSecret .Values.parseable.s3ModeSecret.enabled }}
          {{- range $secret := .Values.parseable.s3ModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if and .Values.parseable.gcsModeSecret .Values.parseable.gcsModeSecret.enabled }}
          {{- range $secret := .Values.parseable.gcsModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey | replace "GCS" "S3"}}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if and .Values.parseable.blobModeSecret .Values.parseable.blobModeSecret.enabled }}
          {{- range $secret := .Values.parseable.blobModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}
          - name: P_MODE
            value: "ingest"
          {{- if .Values.parseable.kafkaConnector.enabled }}
            {{- range $key, $value := .Values.parseable.kafkaConnector.env }}
          - name: {{ $key }}
            value: {{ tpl $value $ | quote }}
            {{- end }}
          {{- end }}

        ports:
          - containerPort: {{ .Values.parseable.highAvailability.ingestor.port }}
      {{- with .Values.readinessProbe }}
        readinessProbe:
          {{ toYaml . | nindent 12 }}
      {{- end }}
        resources:
          {{- toYaml .Values.parseable.highAvailability.ingestor.resources | nindent 12 }}
        {{- if .Values.parseable.persistence.ingestor.enabled }}
        volumeMounts:
        - mountPath: "/parseable/staging"
          name: stage-volume
        {{- end }}
  volumeClaimTemplates:
  {{- if .Values.parseable.persistence.ingestor.enabled }}
  - metadata:
      name: stage-volume
    spec:
      accessModes:
      - {{ .Values.parseable.persistence.ingestor.accessMode | quote }}
      storageClassName: {{ .Values.parseable.persistence.ingestor.storageClass | quote }}
      resources:
        requests:
          storage: {{ .Values.parseable.persistence.ingestor.size | quote }}
  {{- end }}
{{- end }}