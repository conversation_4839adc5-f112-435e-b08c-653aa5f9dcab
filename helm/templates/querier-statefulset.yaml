{{- if .Values.parseable.highAvailability.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "parseable.fullname" . }}-querier-headless
  namespace: {{ .Release.Namespace }}
spec:
  ports:
    - port: 8000
      name: "parseable-port"
  clusterIP: None
  selector:
    {{- include "parseable.querierLabelsSelector" . | nindent 4 }}
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "parseable.fullname" . }}-querier
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "parseable.querierLabels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "parseable.querierLabelsSelector" . | nindent 6 }}
  serviceName: {{ include "parseable.fullname" . }}-querier-headless
  ## TODO: this will change as we scale queriers
  replicas: 1
  minReadySeconds: 2
  template:
    metadata:
      annotations:
      {{- .Values.parseable.podAnnotations | toYaml | nindent 8 }}
      labels:
        {{- .Values.parseable.podLabels | toYaml | nindent 8 }}
        {{- include "parseable.querierLabelsSelector" . | nindent 8 }}
    spec:
      terminationGracePeriodSeconds: 10
      serviceAccountName: {{ include "parseable.serviceAccountName" . }}
      {{- with .Values.parseable.toleration }}
      tolerations:
        {{ toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.parseable.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        securityContext:
          {{- toYaml .Values.parseable.securityContext | nindent 10 }}
        image: {{ .Values.parseable.image.repository }}:{{ .Values.parseable.image.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.parseable.image.pullPolicy }}
        args: ["/usr/bin/parseable", {{ if eq .Values.parseable.store "gcs-store" }}"s3-store"{{ else }}{{ .Values.parseable.store | quote }}{{ end }}]
        env:
          - name: HOSTNAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: P_MODE
            value: "query"
          {{- if .Values.parseable.persistence.querier.enabled }}
          - name: P_HOT_TIER_DIR
            value: "/parseable/hot-tier"
          - name: P_MAX_DISK_USAGE_PERCENT
            value: "95.0"
          {{- end }}
          {{- range $key, $value :=  .Values.parseable.env }}
          - name: {{ $key }}
            value: {{ tpl $value $ | quote }}
          {{- end }}

          {{- if .Values.parseable.auditLogging.enabled }}
          - name: P_AUDIT_LOGGER
            value: {{ .Values.parseable.auditLogging.p_server | quote }}
          - name: P_AUDIT_USERNAME
            value: {{ .Values.parseable.auditLogging.p_username | quote }}
          - name: P_AUDIT_PASSWORD
            value: {{ .Values.parseable.auditLogging.p_password | quote }}
          {{- end }}

          {{- if and .Values.parseable.s3ModeSecret .Values.parseable.s3ModeSecret.enabled }}
          {{- range $secret := .Values.parseable.s3ModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if and .Values.parseable.gcsModeSecret .Values.parseable.gcsModeSecret.enabled }}
          {{- range $secret := .Values.parseable.gcsModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey | replace "GCS" "S3"}}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}
          
          {{- if and .Values.parseable.blobModeSecret .Values.parseable.blobModeSecret.enabled }}
          {{- range $secret := .Values.parseable.blobModeSecret.secrets }}
          {{- range $key := $secret.keys }}
          {{- $envPrefix := $secret.prefix | default "" | upper }}
          {{- $envKey := $key | upper | replace "." "_" | replace "-" "_" }}
          - name: {{ $envPrefix }}{{ $envKey }}
            valueFrom:
              secretKeyRef:
                name: {{ $secret.name }}
                key: {{ $key }}
          {{- end }}
          {{- end }}
          {{- end }}
        ports:
          - containerPort: 8000
        {{- with .Values.parseable.readinessProbe }}
        readinessProbe:
          {{ toYaml . | nindent 12 }}
        {{- end }}
        resources:
          {{- toYaml .Values.parseable.resources | nindent 12 }}
        volumeMounts:
        - mountPath: "/parseable/staging"
          name: stage-volume
        {{- if .Values.parseable.persistence.querier.enabled }}
        - mountPath: "/parseable/hot-tier"
          name: hot-tier-volume
        {{- end }}
      volumes:
      - emptyDir: {}
        name: stage-volume
      {{- if .Values.parseable.sidecar.enabled}}
      - name: {{ .Chart.Name }}-sidecar
        securityContext:
          {{- toYaml .Values.parseable.securityContext | nindent 8 }}
        image: {{ .Values.parseable.sidecar.image.repository }}:{{ .Values.parseable.sidecar.image.tag }}
        imagePullPolicy: {{ .Values.parseable.sidecar.image.pullPolicy }}
        command: {{ .Values.parseable.sidecar.command  }}
        args: {{ .Values.parseable.sidecar.args  }}
        env:
          {{- range $key, $value :=  .Values.parseable.sidecar.env }}
          - name: {{ $key }}
            value: {{ tpl $value $ | quote }}
          {{- end }}
        ports:
          - containerPort: {{ .Values.parseable.sidecar.ports }}
        resources:
          {{- toYaml .Values.parseable.sidecar.resources | nindent 10 }}
        volumeMounts: {{ .Values.parseable.sidecar.volumeMounts | toYaml | nindent 10 }}
      {{- end }}
  volumeClaimTemplates:
  {{- if .Values.parseable.persistence.querier.enabled }}
  - metadata:
      name: hot-tier-volume
    spec:
      accessModes:
      - {{ .Values.parseable.persistence.querier.accessMode | quote }}
      storageClassName: {{ .Values.parseable.persistence.querier.storageClass | quote }}
      resources:
        requests:
          storage: {{ .Values.parseable.persistence.querier.size | quote }}
  - metadata:
      name: stage-volume
    spec:
      accessModes:
      - {{ .Values.parseable.persistence.querier.accessMode | quote }}
      storageClassName: {{ .Values.parseable.persistence.querier.storageClass | quote }}
      resources:
        requests:
          storage: 5Gi
  {{- end }}
  {{- if .Values.parseable.sidecar.enabled}}
    {{- .Values.parseable.sidecar.volumeClaimTemplates | toYaml | nindent 4 }}
  {{- end }}
{{- end }}
