parseable:
  image:
    repository: parseable/parseable
    tag: "v2.3.3"
    pullPolicy: Always
  ## object store can be local-store, s3-store, blob-store or gcs-store.
  store: local-store
  ## Set to true if you want to deploy Parseable in a HA mode (multiple ingestors + hot tier)
  ## Please note that highAvailability is not supported in local mode
  highAvailability:
    enabled: false
    ingestor:
      affinity: { }
        # podAntiAffinity:
        #   requiredDuringSchedulingIgnoredDuringExecution:
        #   - labelSelector:
        #       matchLabels:
        #         app: parseable
      #         component: ingestor
      #     topologyKey: kubernetes.io/hostname
      port: 8000
      extraLabels:
        app: parseable
      podAnnotations: { }
      nodeSelector: { }
      tolerations: [ ]
      labels:
        app: parseable
        component: ingestor
      count: 3
      env:
        RUST_LOG: warn
      ## Use this endpoint to send events to ingestors
      ## Console (UI) is available on the other service (that points to the query pod)
      service:
        type: ClusterIP
        port: 80
      readinessProbe:
        httpGet:
          path: /api/v1/readiness
          port: 8000
      resources:
        limits:
          cpu: 500m
          memory: 4Gi
        requests:
          cpu: 250m
          memory: 1Gi
  ## Enable audit logging on parseable nodes
  auditLogging:
    enabled: false
    p_server: "http://parseable-ingestor-service.parseable.svc.cluster.local"
    p_username: "admin"
    p_password: "admin"

  ## Add environment variables to the Parseable Deployment
  env:
    RUST_LOG: warn
  ## Enable to create a log stream and then add retention configuration
  ## for that log stream
  # logstream:
  #   - name: "vectordemo"
  #     retention:
  #       action: "delete"
  #       duration: "30d"
  #   - name: "fluentbitdemo"
  #     retention:
  #       action: "delete"
  #       duration: "30d"
  ## enable persistence using PVC for the Data and Staging directories
  ## Note that Data directory is needed only for local mode
  persistence:
    staging:
      enabled: true
      storageClass: ""
      accessMode: ReadWriteOnce
      size: 5Gi
    ingestor:
      enabled: false
      storageClass: ""
      accessMode: ReadWriteOnce
      size: 5Gi
    data:
      enabled: false
      storageClass: ""
      accessMode: ReadWriteOnce
      size: 5Gi
    ## enabling the disk on querier adds the hot-tier.
    ## local-store is not supported on hot-tier.
    querier:
      enabled: false
      storageClass: ""
      accessMode: ReadWriteOnce
      size: 100Gi
  ## comment out the secrets depending upon deployment option
  ## localModeSecret if store is set to local
  ## blobModeSecret if store is set to blob-store
  ## s3ModeSecret if store is set to s3-store
  localModeSecret:
    enabled: false
    secrets:
      - name: parseable-env-secret
        prefix: P_
        keys:
          - addr
          - username
          - password
          - staging.dir
          - fs.dir
  blobModeSecret:
    enabled: false
    secrets:
      - name: parseable-env-secret
        prefix: P_
        keys:
          - addr
          - username
          - password
          - azr.access_key
          - azr.account
          - azr.container
          - azr.url
  s3ModeSecret:
    enabled: false
    secrets:
      - name: parseable-env-secret
        prefix: P_
        keys:
          - addr
          - username
          - password
          - staging.dir
          - fs.dir
          - s3.url
          - s3.access.key
          - s3.secret.key
          - s3.bucket
          - s3.region
  gcsModeSecret:
    enabled: false
    secrets:
      - name: parseable-env-secret
        prefix: P_
        keys:
          - addr
          - username
          - password
          - staging.dir
          - fs.dir
          - gcs.url
          - gcs.access.key
          - gcs.secret.key
          - gcs.bucket
          - gcs.region
  serviceAccount:
    create: true
    name: "parseable"
    annotations: { }
  nodeSelector: { }
  service:
    type: ClusterIP
    port: 80
  readinessProbe:
    httpGet:
      path: /api/v1/readiness
      port: 8000
  toleration: [ ]
  resources:
    limits:
      cpu: 500m
      memory: 4Gi
    requests:
      cpu: 250m
      memory: 1Gi
  ## works only when highAvailability is enabled
  ## Set it to true if you want to deploy Parseable 
  ## Query node with a sidecar
  sidecar:
    enabled: false
    image:
      repository: busybox
      tag: latest
      pullPolicy: IfNotPresent
    command: [ ]
    args: [ ]
    env:
      RUST_LOG: warn
    ports: 8000
    volumeMounts:
      - name: test-volume
        mountPath: /parseable/test
    volumeClaimTemplates:
      - metadata:
          name: test-volume
        spec:
          accessModes: [ "ReadWriteOnce" ]
          resources:
            requests:
              storage: 1Gi
    resources:
      limits:
        cpu: 500m
        memory: 4Gi
      requests:
        cpu: 250m
        memory: 1Gi
  securityContext:
    allowPrivilegeEscalation: false
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "80"
    prometheus.io/path: "/api/v1/metrics"
  podSecurityContext:
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    fsGroupChangePolicy: "Always"
  nameOverride: ""
  fullnameOverride: ""
  affinity: { }
  podLabels:
    app: parseable
    component: query
  tolerations: [ ]
  ## Use this section to create ServiceMonitor object for
  ## this Parseable deployment. Read more on ServiceMonitor
  ## here: https://prometheus-operator.dev/docs/api-reference/api/#monitoring.coreos.com/v1.ServiceMonitor
  metrics:
    serviceMonitor:
      enabled: false
      labels: { }
      namespace: ""
      spec:
        jobLabel: ""
        targetLabels: [ ]
        podTargetLabels: [ ]
        endpoints: [ ]
        selector: { }
        namespaceSelector: { }
        sampleLimit: 0
        scrapeProtocols: [ ]
        targetLimit: 0
        labelLimit: 0
        labelNameLengthLimit: 0
        labelValueLengthLimit: 0
        keepDroppedTargets: 0
        attachMetadata: { }
        scrapeClass: ""
        bodySizeLimit: { }
  kafkaConnector:
    enabled: false
    env:
      # General Kafka Configuration
      P_KAFKA_BOOTSTRAP_SERVERS: "my-kafka.kafka.svc.cluster.local:9092" # Comma-separated list of Kafka bootstrap servers
      P_KAFKA_PARTITION_LISTENER_CONCURRENCY: "2" # Number of parallel threads for Kafka partition listeners
      P_KAFKA_CONSUMER_TOPICS: "test-log-stream-0,test-log-stream-1"
      # P_KAFKA_CLIENT_ID: "parseable-connect" # Client ID for Kafka connection
      # P_CONNECTOR_BAD_DATA_POLICY: "fail" # Default Policy for handling bad data

      # Consumer Configurations. These config are Default Parseable KafkaConnector configs. Change only if necessary.
      # P_KAFKA_CONSUMER_GROUP_ID: "parseable-connect-cg"
      # P_KAFKA_CONSUMER_BUFFER_SIZE: "10000"
      # P_KAFKA_CONSUMER_BUFFER_TIMEOUT: "10000ms"
      # P_KAFKA_CONSUMER_SESSION_TIMEOUT: "60000"
      # P_KAFKA_CONSUMER_HEARTBEAT_INTERVAL: "3000"
      # P_KAFKA_CONSUMER_PARTITION_STRATEGY: "roundrobin,range"
      # P_KAFKA_CONSUMER_MAX_POLL_INTERVAL: "300000"
      # P_KAFKA_CONSUMER_ENABLE_AUTO_OFFSET_STORE: "true"
      # P_KAFKA_CONSUMER_AUTO_OFFSET_RESET: "earliest"
      # P_KAFKA_CONSUMER_FETCH_MIN_BYTES: "1"
      # P_KAFKA_CONSUMER_FETCH_MAX_BYTES: "52428800"
      # P_KAFKA_CONSUMER_FETCH_MAX_WAIT: "500"
      # P_KAFKA_CONSUMER_MAX_PARTITION_FETCH_BYTES: "1048576"
      # P_KAFKA_CONSUMER_QUEUED_MIN_MESSAGES: "100000"
      # P_KAFKA_CONSUMER_QUEUED_MAX_MESSAGES_KBYTES: "65536"
      # P_KAFKA_CONSUMER_ENABLE_PARTITION_EOF: "false"
      # P_KAFKA_CONSUMER_CHECK_CRCS: "false"
      # P_KAFKA_CONSUMER_ISOLATION_LEVEL: "read_committed"
      # P_KAFKA_CONSUMER_FETCH_MESSAGE_MAX_BYTES: "1048576"
      # P_KAFKA_CONSUMER_STATS_INTERVAL: "10000"

      # Security Configuration Options - By Default PLAINTEXT

      # Option 1: SSL Encryption Only
      # P_KAFKA_SECURITY_PROTOCOL: "SSL"
      # P_KAFKA_SSL_CA_LOCATION: "/etc/ssl/certs/ca-certificates.crt"
      # P_KAFKA_SSL_CERTIFICATE_LOCATION: "/etc/ssl/certs/client-cert.pem"
      # P_KAFKA_SSL_KEY_LOCATION: "/etc/ssl/private/client-key.pem"
      # P_KAFKA_SSL_KEY_PASSWORD: "my-key-password" # Optional: only if key is password protected

      # Option 2: SASL Authentication with SSL Encryption
      # P_KAFKA_SECURITY_PROTOCOL: "SASL_SSL"
      # P_KAFKA_SSL_CA_LOCATION: "/etc/ssl/certs/ca-certificates.crt"
      # P_KAFKA_SASL_MECHANISM: "SCRAM-SHA-512" # Can also be PLAIN, SCRAM-SHA-256, or GSSAPI
      # P_KAFKA_SASL_USERNAME: "kafka-user"
      # P_KAFKA_SASL_PASSWORD: "kafka-password"

      # Option 3: SASL Authentication without Encryption
      # P_KAFKA_SECURITY_PROTOCOL: "SASL_PLAINTEXT"
      # P_KAFKA_SASL_MECHANISM: "PLAIN" # Can also be SCRAM-SHA-256, SCRAM-SHA-512, or GSSAPI
      # P_KAFKA_SASL_USERNAME: "kafka-user"
      # P_KAFKA_SASL_PASSWORD: "kafka-password"

      # Option 4: OAuth Bearer Token Authentication (Not supported yet)
      # P_KAFKA_SECURITY_PROTOCOL: "SASL_SSL"
      # P_KAFKA_SASL_MECHANISM: "OAUTHBEARER"
      # P_KAFKA_OAUTH_TOKEN_ENDPOINT: "https://oauth.example.com/token"
      # P_KAFKA_OAUTH_CLIENT_ID: "kafka-client"
      # P_KAFKA_OAUTH_CLIENT_SECRET: "client-secret"
      # P_KAFKA_OAUTH_SCOPE: "kafka-access" # Optional: only if required by OAuth provider

## Default values for Vector
# See Vector helm documentation to learn more:
# https://vector.dev/docs/setup/installation/package-managers/helm/
vector:
  enabled: false
  role: "Agent"
  rollWorkload: true
  image:
    repository: timberio/vector
    pullPolicy: IfNotPresent
    pullSecrets: [ ]
    tag: ""
    sha: ""
  replicas: 1
  podManagementPolicy: OrderedReady
  podDisruptionBudget:
    enabled: false
    minAvailable: 1
    maxUnavailable:
  rbac:
    create: true
  serviceAccount:
    create: true
    annotations: { }
    name:
    automountToken: true
  podLabels:
    vector.dev/exclude: "true"
  args:
    - --config-dir
    - "/etc/vector/"
  terminationGracePeriodSeconds: 60
  service:
    enabled: true
    type: "ClusterIP"
    annotations: { }
    topologyKeys: [ ]
    ports: [ ]
    externalTrafficPolicy: ""
    loadBalancerIP: ""
    ipFamilyPolicy: ""
    ipFamilies: [ ]
  serviceHeadless:
    enabled: true
  dnsPolicy: ClusterFirst
  customConfig:
    data_dir: /vector-data-dir
    api:
      enabled: true
      address: 127.0.0.1:8686
      playground: false
    sources:
      kubernetes_logs:
        type: kubernetes_logs
    sinks:
      parseable:
        type: http
        method: post
        batch:
          max_bytes: ********
          max_events: 1000
          timeout_secs: 10
        compression: gzip
        inputs:
          - kubernetes_logs
        encoding:
          codec: json
        uri: 'http://parseable.parseable.svc.cluster.local/api/v1/ingest'
        auth:
          strategy: basic
          user: admin
          password: admin
        request:
          headers:
            X-P-Stream: vectordemo
        healthcheck:
          enabled: true
          path: 'http://parseable.parseable.svc.cluster.local/api/v1/liveness'
          port: 80

# Default values for fluent-bit.
# See fluent-bit helm documentation to learn more:
# https://github.com/fluent/helm-charts/tree/main/charts/fluent-bit
fluent-bit:
  enabled: false
  kind: DaemonSet
  serverHost: parseable-ingestor-service.parseable.svc.cluster.local
  serverUsername: admin
  serverPassword: admin
  serverStream: $NAMESPACE
  excludeNamespaces: kube-system, default
  replicaCount: 1
  image:
    repository: parseable/fluent-bit
    tag: "v2"
    pullPolicy: Always
  testFramework:
    enabled: true
    image:
      repository: busybox
      pullPolicy: Always
      tag: latest
  serviceAccount:
    create: true
    annotations: { }
    name:
  rbac:
    create: true
    nodeAccess: false
    eventsAccess: true
  dnsPolicy: ClusterFirst
  service:
    type: ClusterIP
    port: 2020
    loadBalancerClass:
    loadBalancerSourceRanges: [ ]
    labels: { }
  livenessProbe:
    httpGet:
      path: /
      port: http
  readinessProbe:
    httpGet:
      path: /api/v1/health
      port: http
  flush: 1
  metricsPort: 2020
  ## https://docs.fluentbit.io/manual/administration/configuring-fluent-bit/classic-mode/configuration-file
  config:
    service: |
      [SERVICE]
          Daemon Off
          Flush {{ .Values.flush }}
          Log_Level {{ .Values.logLevel }}
          Parsers_File parsers.conf
          Parsers_File custom_parsers.conf
          HTTP_Server On
          HTTP_Listen 0.0.0.0
          HTTP_Port {{ .Values.metricsPort }}
          Health_Check On

    ## https://docs.fluentbit.io/manual/pipeline/inputs
    inputs: |
      [INPUT]
          Name tail
          Path /var/log/containers/*.log
          multiline.parser docker, cri
          Tag kube.*
          Mem_Buf_Limit 5MB
          Skip_Long_Lines On

      # [INPUT]
      #     Name tail
      #     Path /var/log/containers/{NGINX_POD_NAME}-*.log
      #     Parser nginx_access
      #     Tag kube.*
      #     Mem_Buf_Limit 5MB
      #     Skip_Long_Lines On

      # [INPUT]
      #     Name tail
      #     Path /var/log/containers/{NGINX_POD_NAME}-*.log
      #     Parser nginx_error
      #     Tag kube.*
      #     Mem_Buf_Limit 5MB
      #     Skip_Long_Lines On

      [INPUT]
          name kubernetes_events
          tag k8s_events

      [INPUT]
          Name systemd
          Tag host.*
          Systemd_Filter _SYSTEMD_UNIT=kubelet.service
          Read_From_Tail On

    ## https://docs.fluentbit.io/manual/pipeline/filters
    filters: |
      [FILTER]
          Name                kubernetes
          Match               kube.*
          Merge_Log           On
          Keep_Log            Off
          K8S-Logging.Parser  On
          K8S-Logging.Exclude On

    ## https://docs.fluentbit.io/manual/pipeline/outputs
    outputs: |
      [OUTPUT]
          Name parseable
          Match kube.*
          Server_Host {{ .Values.serverHost }}
          Username {{ .Values.serverUsername }}
          Password {{ .Values.serverPassword }}
          Server_Port 80
          Stream {{ .Values.serverStream }}
          Exclude_Namespaces {{ .Values.excludeNamespaces }}

      [OUTPUT]
          Name parseable
          Match k8s_events
          Server_Host {{ .Values.serverHost }}
          Server_Port 80
          Username {{ .Values.serverUsername }}
          Password {{ .Values.serverPassword }}
          Stream k8s-events 

    upstream: { }

    customParsers: |
      [PARSER]
          Name docker_no_time
          Format json
          Time_Keep Off
          Time_Key time
          Time_Format %Y-%m-%dT%H:%M:%S.%L

      # [PARSER]
      #     Name     nginx_error
      #     Format   regex
      #     Regex    ^(?<timestamp>\S+Z) stderr F (?<error_timestamp>\S+ \S+) \[(?<log_level>\S+)\] (?<process_id>\d+#\d+): \*(?<request_id>\d+) (?<error_message>.*?) client: (?<client_ip>\S+), server: (?<server_name>\S+), request: "(?<request_method>\S+) (?<request_path>\S+) HTTP/\S+", upstream: "(?<upstream_url>[^"]+)", host: "(?<host>\S+)"$
      #     Time_Key  timestamp
      #     Time_Format %Y/%m/%d %H:%M:%S
      
      # [PARSER]
      #     Name   nginx_access
      #     Format regex
      #     Regex  (?<remote_addr>[^ ]*) - (?<remote_user>[^ ]*) \[(?<timestamp>[^\]]*)\] "(?<method>\S+)(?: +(?<request>[^\"]*?)(?: +\S*)?)?" (?<status>[^ ]*) (?<body_bytes_sent>[^ ]*) "(?<http_referer>[^\"]*)" "(?<http_user_agent>[^\"]*)" (?<request_length>[^ ]*) (?<request_time>[^ ]*) \[(?<proxy_upstream_name>[^ ]*)\] \[(?<proxy_alternative_upstream_name>[^ ]*)\] (?<upstream_addr>[^,]*),?(?:[^,]*),?(?:[^ ]*) (?<upstream_response_length>[^,]*),?(?:[^,]*),?(?:[^ ]*) (?<upstream_response_time>[^,]*),?(?:[^,]*),?(?:[^ ]*) (?<upstream_status>[^,]*),?(?:[^,]*),?(?:[^ ]*) (?<req_id>[^ ]*)
      #     Time_Key timestamp
      #     Time_Format %d/%b/%Y:%H:%M:%S %z

  # The config volume is mounted by default, either to the existingConfigMap value, or the default of "fluent-bit.fullname"
  volumeMounts:
    - name: config
      mountPath: /fluent-bit/etc/fluent-bit.conf
      subPath: fluent-bit.conf
    - name: config
      mountPath: /fluent-bit/etc/custom_parsers.conf
      subPath: custom_parsers.conf
  daemonSetVolumes:
    - name: varlog
      hostPath:
        path: /var/log
    - name: varlibdockercontainers
      hostPath:
        path: /var/lib/docker/containers
    - name: etcmachineid
      hostPath:
        path: /etc/machine-id
        type: File
  daemonSetVolumeMounts:
    - name: varlog
      mountPath: /var/log
    - name: varlibdockercontainers
      mountPath: /var/lib/docker/containers
      readOnly: true
    - name: etcmachineid
      mountPath: /etc/machine-id
      readOnly: true
  logLevel: info
