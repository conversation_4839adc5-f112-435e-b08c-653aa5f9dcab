global
    log stdout format raw local0
    maxconn 60000
    daemon

defaults
    log     global
    mode    http
    option  httplog
    option  dontlognull
    timeout connect 5000
    timeout client  50000
    timeout server  50000

frontend stats
    bind *:9001
    stats enable
    stats uri /
    stats refresh 30s
    stats admin if TRUE

frontend ingestion_frontend
    bind *:8001
    mode http
    default_backend ingestion_backend

backend ingestion_backend
    mode http
    balance roundrobin
    option forwardfor

    # Health check configuration
    option httpchk GET /api/v1/liveness
    http-check expect status 200

    # Backend servers
    server ingest1 parseable-ingest-one:8000 check inter 5s rise 2 fall 3
    server ingest2 parseable-ingest-two:8000 check inter 5s rise 2 fall 3

    # Session persistence
    hash-type consistent

    # Retry configuration
    retries 3
    option redispatch
