name: Ensure parseable builds on all release targets
on:
  pull_request:
    paths-ignore:
      - docs/**
      - helm/**
      - assets/**
      - "**.md"
jobs:
  # Default build without Kafka
  build-default:
    name: Build Default ${{matrix.target}}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux builds
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          - os: ubuntu-latest
            target: aarch64-unknown-linux-gnu
          # macOS builds  
          - os: macos-latest
            target: x86_64-apple-darwin
          - os: macos-latest
            target: aarch64-apple-darwin
          # Windows build
          - os: windows-latest
            target: x86_64-pc-windows-msvc
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ matrix.target }}-default-${{ hashFiles('**/Cargo.lock') }}
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          use-cross: ${{ runner.os == 'Linux' }}
          command: build
          args: --target ${{ matrix.target }} --release
  # Kafka build for supported platforms
  build-kafka:
    name: Build Kafka ${{matrix.target}}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux builds
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          - os: macos-latest
            target: aarch64-apple-darwin
    steps:
      - uses: actions/checkout@v4
      # Linux-specific dependencies
      - name: Install Linux dependencies
        if: runner.os == 'Linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            pkg-config \
            cmake \
            clang \
            zlib1g-dev \
            libzstd-dev \
            liblz4-dev \
            libssl-dev \
            libsasl2-dev \
            python3 \
            gcc-aarch64-linux-gnu \
            g++-aarch64-linux-gnu
          # Install cross-compilation specific packages
          if [ "${{ matrix.target }}" = "aarch64-unknown-linux-gnu" ]; then
            sudo apt-get install -y \
              gcc-aarch64-linux-gnu \
              g++-aarch64-linux-gnu \
              libc6-dev-arm64-cross \
              libsasl2-dev:arm64 \
              libssl-dev:arm64 \
              pkg-config-aarch64-linux-gnu
          fi
      # macOS-specific dependencies
      - name: Install macOS dependencies
        if: runner.os == 'macOS'
        run: |
          brew install \
            cmake \
            llvm \
            pkg-config \
            zstd \
            lz4 \
            openssl@3.0 \
            cyrus-sasl \
            python3
      - name: Setup Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ matrix.target }}-kafka-${{ hashFiles('**/Cargo.lock') }}
      - name: Find and fix librdkafka CMakeLists.txt for Linux
        if: runner.os == 'Linux'
        run: |
          cargo fetch
          # Find the rdkafka-sys package directory
          RDKAFKA_SYS_DIR=$(find ~/.cargo/registry/src -name "rdkafka-sys-*" -type d | head -n 1)
          echo "Found rdkafka-sys at: $RDKAFKA_SYS_DIR"
          # Find the librdkafka CMakeLists.txt file
          CMAKE_FILE="$RDKAFKA_SYS_DIR/librdkafka/CMakeLists.txt"
          if [ -f "$CMAKE_FILE" ]; then
            echo "Found CMakeLists.txt at: $CMAKE_FILE"
            # Make a backup of the original file
            cp "$CMAKE_FILE" "$CMAKE_FILE.bak"
            # Replace the minimum required version
            sed -i 's/cmake_minimum_required(VERSION 3.2)/cmake_minimum_required(VERSION 3.5)/' "$CMAKE_FILE"
            echo "Modified CMakeLists.txt - before and after comparison:"
            diff "$CMAKE_FILE.bak" "$CMAKE_FILE" || true
          else
            echo "Could not find librdkafka CMakeLists.txt file!"
            exit 1
          fi
      - name: Find and fix librdkafka CMakeLists.txt for macOS
        if: runner.os == 'macOS'
        run: |
          cargo fetch
          # Find the rdkafka-sys package directory
          RDKAFKA_SYS_DIR=$(find ~/.cargo/registry/src -name "rdkafka-sys-*" -type d | head -n 1)
          echo "Found rdkafka-sys at: $RDKAFKA_SYS_DIR"
          # Find the librdkafka CMakeLists.txt file
          CMAKE_FILE="$RDKAFKA_SYS_DIR/librdkafka/CMakeLists.txt"
          if [ -f "$CMAKE_FILE" ]; then
            echo "Found CMakeLists.txt at: $CMAKE_FILE"
            # Make a backup of the original file
            cp "$CMAKE_FILE" "$CMAKE_FILE.bak"
            # Replace the minimum required version - macOS requires '' after -i
            sed -i '' 's/cmake_minimum_required(VERSION 3.2)/cmake_minimum_required(VERSION 3.5)/' "$CMAKE_FILE"
            echo "Modified CMakeLists.txt - before and after comparison:"
            diff "$CMAKE_FILE.bak" "$CMAKE_FILE" || true
          else
            echo "Could not find librdkafka CMakeLists.txt file!"
            exit 1
          fi
      - name: Build with Kafka
        uses: actions-rs/cargo@v1
        with:
          use-cross: ${{ runner.os == 'Linux' }}
          command: build
          args: --target ${{ matrix.target }} --features kafka --release
        env:
          LIBRDKAFKA_SSL_VENDORED: 1
          PKG_CONFIG_ALLOW_CROSS: "1"
          PKG_CONFIG_PATH: /usr/lib/aarch64-linux-gnu/pkgconfig
          SASL2_DIR: /usr/lib/aarch64-linux-gnu
          OPENSSL_DIR: /usr/lib/aarch64-linux-gnu
          OPENSSL_ROOT_DIR: /usr/lib/aarch64-linux-gnu
          OPENSSL_STATIC: "1"
          SASL2_STATIC: "0"