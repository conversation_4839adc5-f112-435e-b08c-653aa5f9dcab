on:
  pull_request:
    paths-ignore:
      - "docs/**"
      - "helm/**"
      - "assets/**"
      - "**.md"

name: Lint, Test and Coverage Report
jobs:
  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy

      - uses: Swatinem/rust-cache@v2
        with:
          shared-key: ${{ runner.os }}-cargo

      - uses: taiki-e/install-action@v2
        with:
          tool: cargo-hack, cargo-llvm-cov, nextest

      - name: Install System Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libsasl2-dev \
            libssl-dev \
            pkg-config \
            build-essential
        if: runner.os == 'Linux'
        
      - name: Find and fix librdkafka CMakeLists.txt
        run: |
          # Download the package first so it's in the registry
          cargo fetch
          
          # Find the rdkafka-sys package directory
          RDKAFKA_SYS_DIR=$(find ~/.cargo/registry/src -name "rdkafka-sys-*" -type d | head -n 1)
          echo "Found rdkafka-sys at: $RDKAFKA_SYS_DIR"
          
          # Find the librdkafka CMakeLists.txt file
          CMAKE_FILE="$RDKAFKA_SYS_DIR/librdkafka/CMakeLists.txt"
          
          if [ -f "$CMAKE_FILE" ]; then
            echo "Found CMakeLists.txt at: $CMAKE_FILE"
            
            # Make a backup of the original file
            cp "$CMAKE_FILE" "$CMAKE_FILE.bak"
            
            # Replace the minimum required version
            sed -i 's/cmake_minimum_required(VERSION 3.2)/cmake_minimum_required(VERSION 3.5)/' "$CMAKE_FILE"
            
            echo "Modified CMakeLists.txt - before and after comparison:"
            diff "$CMAKE_FILE.bak" "$CMAKE_FILE" || true
          else
            echo "Could not find librdkafka CMakeLists.txt file!"
            exit 1
          fi

      - name: Check with clippy
        run: cargo hack clippy --verbose --each-feature --no-dev-deps -- -D warnings

      - name: Test default feature set
        run: cargo hack llvm-cov --no-report nextest

      - name: Test kafka feature
        run: cargo hack --features kafka llvm-cov --no-report nextest --filter-expr 'test(kafka)'

      - name: Generate coverage report
        run: cargo llvm-cov report --lcov --output-path coverage.lcov

      - name: Upload Coverage Report
        uses: coverallsapp/github-action@v2.2.3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          file: ./coverage.lcov
          parallel: true
          flag-name: run-${{ matrix.os }}-cargo

      - name: Finish Coverage Report
        uses: coverallsapp/github-action@v2.2.3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel-finished: true
