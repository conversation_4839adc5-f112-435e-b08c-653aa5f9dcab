name: Integration Tests with <PERSON>
on:
  pull_request:
    paths-ignore:
      - "docs/**"
      - "helm/**"
      - "assets/**"
      - "**.md"

jobs:

  docker-compose-test:
    name: Quest Smoke and Load Tests for Standalone deployments
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Start compose
        run: docker compose -f docker-compose-test.yaml up --build --exit-code-from quest
      - name: Stop compose
        if: always()
        run: docker compose -f docker-compose-test.yaml down -v

  docker-compose-distributed-test:
    name: Quest Smoke and Load Tests for Distributed deployments
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Start compose
        run: docker compose -f docker-compose-distributed-test.yaml up --build --exit-code-from quest
      - name: Stop compose
        if: always()
        run: docker compose -f docker-compose-distributed-test.yaml down -v
        
