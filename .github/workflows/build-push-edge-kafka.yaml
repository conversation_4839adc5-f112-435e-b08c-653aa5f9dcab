name: Build and push edge kafka tag

on:
  push:
    branches:
      - 'main'
    paths-ignore:
      - 'docs/**'
      - 'helm/**'
      - 'assets/**'
      - '**.md'

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:qemu-v8.1.5

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@98669ae865ea3cffbcbaa878cf57c20bbf1c6c38
        with:
          images: parseable/parseable

      - name: Build and push x86_64
        uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
        with:
          context: .
          file: ./Dockerfile.kafka
          push: true
          tags: parseable/parseable:edge-kafka
          platforms: linux/amd64
          build-args: |
            LIB_DIR=x86_64-linux-gnu

      - name: Build and push aarch64
        uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
        with:
          context: .
          file: ./Dockerfile.kafka
          push: true
          tags: parseable/parseable:edge-kafka
          platforms: linux/arm64
          build-args: |
            LIB_DIR=aarch64-linux-gnu
