# This workflow builds Parseable for Linux, Mac, Windows and Docker
# and publishes the artifacts to the GitHub release page.
# It is triggered on push to tags that match the pattern vX.Y.Z
# where X, Y, Z are integers (e.g., v1.0.0, v2.3.4).
# It also creates a checksum file for the artifacts and publishes it to the release page.
# It also builds and pushes a Docker image to Docker Hub with the same version tag.

name: Release builds for Linux(64 bit, Arm), Mac(64bit, Arm), Windows and Docker

on:
  push:
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
    paths-ignore:
      - "docs/**"
      - "helm/**"
      - "assets/**"
      - "**.md"

jobs:
  build-linux:
    name: Build for ${{matrix.target}}
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
      attestations: write
    strategy:
      fail-fast: false
      matrix:
        target:
          - aarch64-unknown-linux-gnu # linux(arm)
          - x86_64-unknown-linux-gnu # linux(64 bit)
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal # minimal component installation (ie, no documentation)
          target: ${{matrix.target}}
          override: true

      - uses: actions-rs/cargo@v1
        with:
          use-cross: true
          command: build
          args: --profile release-lto --target ${{matrix.target}}

      - name: Rename binary
        run: |
          mv target/${{ matrix.target }}/release-lto/parseable Parseable_OSS_${{ matrix.target }}

      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v1
        with:
          subject-path: Parseable_OSS_${{ matrix.target }}

      - name: Create Artifact
        uses: actions/upload-artifact@v4
        with:
          name: Parseable_OSS_${{ matrix.target }}
          path: Parseable_OSS_${{ matrix.target }}

      - name: Publish Archive to Release Page
        uses: softprops/action-gh-release@v0.1.15
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          draft: false
          files: Parseable_OSS_${{ matrix.target }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-windows:
    runs-on: windows-latest
    permissions:
      id-token: write
      contents: write
      attestations: write
    steps:
      - name: Checkout
        uses: actions/checkout@v1

      - name: Install latest rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
          default: true
          override: true

      - name: Build
        run: cargo build --all --profile release-lto --target x86_64-pc-windows-msvc

      - name: Rename binary
        run: |
          mv target/x86_64-pc-windows-msvc/release-lto/PARSEABLE.exe Parseable_OSS_x86_64-pc-windows-msvc.exe

      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v1
        with:
          subject-path: Parseable_OSS_x86_64-pc-windows-msvc.exe

      - name: Create artifact for Windows
        uses: actions/upload-artifact@v4
        with:
          name: Parseable_OSS_x86_64-pc-windows-msvc.exe
          path: Parseable_OSS_x86_64-pc-windows-msvc.exe

      - name: Publish Archive to Release Page
        uses: softprops/action-gh-release@v0.1.15
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          draft: false
          files: Parseable_OSS_x86_64-pc-windows-msvc.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-mac:
    runs-on: macos-latest
    permissions:
      id-token: write
      contents: write
      attestations: write
    strategy:
      matrix:
        target:
          - aarch64-apple-darwin
          - x86_64-apple-darwin

    steps:
      - name: Checkout
        uses: actions/checkout@v1

      - name: Install latest rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
          target: ${{ matrix.target }}
          default: true
          override: true

      - name: Build
        run: |
          cargo build --profile release-lto --target ${{ matrix.target }}
          strip target/${{ matrix.target }}/release-lto/Parseable

      - name: Rename binary
        run: |
          mv target/${{ matrix.target }}/release-lto/Parseable Parseable_OSS_${{ matrix.target }}

      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v1
        with:
          subject-path: Parseable_OSS_${{ matrix.target }}

      - name: Create artifact
        uses: actions/upload-artifact@v4
        with:
          name: Parseable_OSS_${{ matrix.target }}
          path: Parseable_OSS_${{ matrix.target }}

      - name: Publish Archive to Release Page
        uses: softprops/action-gh-release@v0.1.15
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          draft: false
          files: Parseable_OSS_${{ matrix.target }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  create-checksum:
    runs-on: ubuntu-latest
    needs: [build-linux, build-windows, build-mac]
    steps:
      - name: Download artifacts created
        uses: actions/download-artifact@v4.1.7

      - name: Run shasum command
        run: |
          find . -type f -name "Parseable_OSS_*" -exec shasum {} \; | sed 's/.\/.*\///' > checksum.txt

      - name: Create artifact
        uses: actions/upload-artifact@v4
        with:
          name: checksum.txt
          path: checksum.txt

      - name: Publish Check Sum to Release Page
        uses: softprops/action-gh-release@v0.1.15
        if: ${{ startsWith(github.ref, 'refs/tags/') }}
        with:
          draft: false
          files: checksum.txt
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-push-docker-image:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:qemu-v8.1.5

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: parseable/parseable

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: parseable/parseable:${{ github.ref_name }}
          platforms: linux/amd64,linux/arm64
