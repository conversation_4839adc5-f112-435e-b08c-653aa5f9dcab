<!-- Thanks for trying to help us make Parseable be the best it can be! Please fill out as much of the following information as is possible (where relevant, and remove it when irrelevant) to help make the intention and scope of this PR clear in order to ease review. -->

Fixes #XXXX.

<!-- Replace XXXX with the id of the issue fixed in this PR. Remove this section if there is no corresponding issue. Don't reference the issue in the title of this pull-request. -->

### Description

<!-- Describe the goal of this PR -->

<!-- Describe the possible solutions and chosen one with the rationale. -->

<!-- Describe key changes made in the patch. -->

<hr>

This PR has:
- [ ] been tested to ensure log ingestion and log query works.
- [ ] added comments explaining the "why" and the intent of the code wherever would not be obvious for an unfamiliar reader.
- [ ] added documentation for new or modified features or behaviors.
