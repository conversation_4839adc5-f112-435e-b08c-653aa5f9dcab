[package]
name = "parseable"
version = "2.3.3"
authors = ["Parseable Team <<EMAIL>>"]
edition = "2021"
rust-version = "1.83.0"
categories = ["logs", "observability", "metrics", "traces"]
build = "build.rs"

[dependencies]
# Arrow and DataFusion ecosystem
arrow = "54.0.0"
arrow-array = "54.0.0" 
arrow-flight = { version = "54.0.0", features = ["tls"] }
arrow-ipc = { version = "54.0.0", features = ["zstd"] }
arrow-json = "54.0.0"
arrow-schema = { version = "54.0.0", features = ["serde"] }
arrow-select = "54.0.0"
datafusion = "45.0.0"
object_store = { version = "0.11.2", features = ["cloud", "aws", "azure"] }
parquet = "54.0.0"

# Web server and HTTP-related
actix-cors = "0.7.0"
actix-web = { version = "4.9.0", features = ["rustls-0_22"] }
actix-web-httpauth = "0.8"
actix-web-prometheus = { version = "0.1" }
actix-web-static-files = "4.0"
http = "0.2.7"
http-auth-basic = "0.3.3"
tonic = { version = "0.12.3", features = ["tls", "transport", "gzip", "zstd"] }
tonic-web = "0.12.3"
tower-http = { version = "0.6.1", features = ["cors"] }
url = "2.4.0"

# Connectors dependencies
rdkafka = { version = "0.37", optional = true, features = ["cmake-build", "tracing", "libz-static"] }
sasl2-sys = { version = "0.1.22", optional = true, features = ["vendored"] }

# Authentication and Security
argon2 = "0.5.0"
base64 = "0.22.0"
cookie = "0.18.1"
hex = "0.4"
openid = { version = "0.15.0", default-features = false, features = ["rustls"] }
rustls = "0.22.4"
rustls-pemfile = "2.1.2"
sha2 = "0.10.8"

# Serialization and Data Formats
byteorder = "1.4.3"
serde = { version = "1.0", features = ["rc", "derive"] }
serde_json = "1.0"
serde_repr = "0.1.17"

# Async and Runtime
async-trait = "0.1"
futures = "0.3"
futures-util = "0.3"
tokio = { version = "^1.43", default-features = false, features = [
    "sync",
    "macros",
    "fs",
    "rt-multi-thread",
] }
tokio-stream = { version = "0.1", features = ["fs"] }
tokio-util = { version = "0.7" }

# Logging and Metrics
opentelemetry-proto = { git = "https://github.com/parseablehq/opentelemetry-rust", branch = "fix-metrics-u64-serialization" }
prometheus = { version = "0.13", features = ["process"] }
prometheus-parse = "0.2.5"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "time"] }

# Time and Date
chrono = "0.4"
chrono-humanize = "0.2"
humantime = "2.1.0"
humantime-serde = "1.1"

# File System and I/O
fs_extra = "1.3"
path-clean = "1.0.1"
relative-path = { version = "1.7", features = ["serde"] }

# CLI and System
clap = { version = "4.5", default-features = false, features = [
    "std",
    "color",
    "help",
    "derive",
    "env",
    "cargo",
    "error-context",
] }
crossterm = "0.28.1"
hostname = "0.4.0"
human-size = "0.4"
num_cpus = "1.15"
sysinfo = "0.33.1"
uptime_lib = "0.3.0"

# Utility Libraries
anyhow = { version = "1.0", features = ["backtrace"] }
bytes = "1.4"
clokwerk = "0.4"
derive_more = { version = "1", features = ["full"] }
itertools = "0.14"
once_cell = "1.20"
rand = "0.8.5"
regex = "1.7.3"
reqwest = { version = "0.11.27", default-features = false, features = [
    "rustls-tls",
    "json",
    "gzip",
    "brotli",
] } # cannot update cause rustls is not latest `see rustls`
semver = "1.0"
static-files = "0.2"
thiserror = "2.0"
ulid = { version = "1.0", features = ["serde"] }
xxhash-rust = { version = "0.8", features = ["xxh3"] }
futures-core = "0.3.31"

[build-dependencies]
cargo_toml = "0.21"
sha1_smol = { version = "1.0", features = ["std"] }
static-files = "0.2"
ureq = "2.12"
url = "2.5"
vergen-gitcl = { version = "1.0", features = ["build", "cargo", "rustc", "si"] }
zip = { version = "2.3", default-features = false, features = ["deflate"] }
anyhow = "1.0"

[dev-dependencies]
rstest = "0.23.0"
arrow = "54.0.0"
temp-dir = "0.1.14"

[package.metadata.parseable_ui]
assets-url = "https://parseable-prism-build.s3.us-east-2.amazonaws.com/v2.3.2/build.zip"
assets-sha1 = "35cfa3ab692ab0debf6666e5e2e1876fa7de4a02"

[features]
debug = []
kafka = ["rdkafka", "rdkafka/ssl-vendored", "rdkafka/ssl", "rdkafka/sasl", "sasl2-sys", "sasl2-sys/vendored"]

[profile.release-lto]
inherits = "release"
lto = "fat"
codegen-units = 1
