[Unit]
Description=Parseable
Wants=network-online.target
After=network-online.target
AssertFileIsExecutable=/usr/local/bin/parseable

[Service]
WorkingDirectory=/usr/local/

User=parseable-user
Group=parseable-user
ProtectProc=invisible

EnvironmentFile=/etc/default/parseable
ExecStart=/usr/local/bin/parseable local-store

# Let systemd restart this service always
Restart=always

# Specifies the maximum file descriptor number that can be opened by this process
LimitNOFILE=1048576

# Specifies the maximum number of threads this process can create
TasksMax=infinity

# Disable timeout logic and wait until process is stopped
TimeoutStopSec=infinity
SendSIGKILL=no

[Install]
WantedBy=multi-user.target

# Built for ${project.name}-${project.version} (${project.name})
